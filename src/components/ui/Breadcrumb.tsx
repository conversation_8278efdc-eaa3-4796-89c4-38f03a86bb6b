import React from 'react';
import { Link, useLocation } from '@tanstack/react-router';
import { ChevronRightIcon, CheckIcon } from '@heroicons/react/24/outline';
import { useCertificateType, type CertificateType } from '../../hooks/useCertificateType';

// Define page types
type PageType = 'objektdaten' | 'gebaeudedetails1' | 'gebaeudedetails2' | 'fenster' | 'heizung' | 'tww-lueftung' | 'verbrauch' | 'zusammenfassung';

// Define the page configuration for each certificate type
const certificateTypePages: Record<CertificateType, PageType[]> = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

// Page metadata
const pageMetadata: Record<PageType, { title: string; path: string }> = {
  'objektdaten': {
    title: 'Objektdaten',
    path: '/erfassen/objektdaten'
  },
  'gebaeudedetails1': {
    title: 'Gebäudedetails 1',
    path: '/erfassen/gebaeudedetails1'
  },
  'gebaeudedetails2': {
    title: 'Gebäudedetails 2',
    path: '/erfassen/gebaeudedetails2'
  },
  'fenster': {
    title: 'Fenster',
    path: '/erfassen/fenster'
  },
  'heizung': {
    title: 'Heizung',
    path: '/erfassen/heizung'
  },
  'tww-lueftung': {
    title: 'TWW & Lüftung',
    path: '/erfassen/tww-lueftung'
  },
  'verbrauch': {
    title: 'Verbrauch',
    path: '/erfassen/verbrauch'
  },
  'zusammenfassung': {
    title: 'Zusammenfassung',
    path: '/erfassen/zusammenfassung'
  }
};

interface BreadcrumbProps {
  className?: string;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  className = ''
}) => {
  const location = useLocation();
  const { certificateType, isLoading } = useCertificateType();

  // Don't render if certificate type is not loaded yet
  if (isLoading || !certificateType) {
    return null;
  }

  // Get the pages for the current certificate type
  const pages = certificateTypePages[certificateType];
  
  // Determine current page from URL
  const currentPath = location.pathname;
  const currentPageType = Object.entries(pageMetadata).find(
    ([_, meta]) => meta.path === currentPath
  )?.[0] as PageType;
  
  // Find current page index
  const currentPageIndex = pages.findIndex(page => page === currentPageType);
  
  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <div className="flex items-center space-x-1">
        {pages.map((pageType, index) => {
          const isCurrentPage = pageType === currentPageType;
          const isCompleted = index < currentPageIndex;
          const isAccessible = isCompleted || isCurrentPage;
          const metadata = pageMetadata[pageType];
          
          return (
            <React.Fragment key={pageType}>
              {index > 0 && (
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
              )}
              
              <div className="flex items-center">
                {isAccessible ? (
                  <Link
                    to={metadata.path}
                    className={`flex items-center px-3 py-2 rounded-md transition-colors ${
                      isCurrentPage
                        ? 'bg-blue-100 text-blue-800 font-medium'
                        : isCompleted
                        ? 'text-green-600 hover:text-green-800 hover:bg-green-50'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {isCompleted && (
                      <CheckIcon className="h-4 w-4 mr-1 text-green-500" />
                    )}
                    {metadata.title}
                  </Link>
                ) : (
                  <span className="flex items-center px-3 py-2 text-gray-400 cursor-not-allowed">
                    {metadata.title}
                  </span>
                )}
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </nav>
  );
};
